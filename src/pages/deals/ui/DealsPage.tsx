import { MobileSearch } from '@components/mobile-search/ui/MobileSearch';
import type { SeoData } from '@components/SeoHelmet';
import { SeoHelmet } from '@components/SeoHelmet';
import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useIsMobileView } from '@hooks/system/useIsMobileView';
import { getRouteApi } from '@tanstack/react-router';
import { DealsCarousel } from '@widgets/deals/carousel/ui/DealsCarousel';
import { mobileSearchModel } from '@widgets/mobile-search/model';
import { useUnit } from 'effector-react';
import { debounce } from 'lodash';
import { lazy, Suspense, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DealCategoryBreadcrumb } from './DealCategoryBreadcrumb';
import { DealsCategorySection } from './deals-category-section/DealsCategorySection';
import DealMainSection from './deals-main-section/DealMainSection';
import { DealsMobileSearchView } from './deals-mobile-search-view/DealsMobileSearchView';
import { DealsCategoriesSelectMobile } from './DealsCategoriesSelectMobile';
import { DealsSearch } from './DealsSearch';

const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then(
    ({ SubscribeNewsletterPopup }) => ({
      default: SubscribeNewsletterPopup,
    }),
  ),
);

const routeApi = getRouteApi('/_protected/_main/deals');

export const Deals = () => {
  const isMobileView = useIsMobileView();
  const { category, title } = routeApi.useSearch();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const isDealsMainView = !title && !category;

  const seoData: SeoData = useMemo(() => {
    if (category) {
      return null;
    }

    return {
      title: t(LOCIZE_DEALS_KEYS.meta.title),
      description: t(LOCIZE_DEALS_KEYS.meta.description),
      keywords: t(LOCIZE_DEALS_KEYS.meta.keywords),
    };
  }, [category, t]);

  return (
    <>
      <SeoHelmet seoData={seoData} />
      {isMobileView ? (
        <DealsSearch className="fixed z-20 w-full px-6 py-4 md:px-12 bg-primary-white" />
      ) : null}

      <DealsCarousel className={'mt-20 pb-10 md:mt-0 md:px-12 md:py-12'} />

      {isDealsMainView && (
        <div className="translate-y-5 animate-fade-in-up opacity-0">
          <div className="text-center p-6 pt-0">
            <Typography variant={isMobileView ? 'l' : 'xl'}>
              {t(LOCIZE_DEALS_KEYS.title)}
            </Typography>
            <Typography className="mt-6">
              {t(LOCIZE_DEALS_KEYS.description)}
            </Typography>
          </div>
        </div>
      )}

      <div className="grid gap-10 pb-12">
        {!isMobileView ? <DealsSearch className="px-6 md:px-12" /> : null}

        {isMobileView && !category && (
          <Suspense
            fallback={<Skeleton className="h-[5.4rem] w-full rounded-2xl" />}
          >
            <DealsCategoriesSelectMobile />
          </Suspense>
        )}

        <Suspense
          fallback={
            <Skeleton className="mx-6 h-[20rem] rounded-2xl md:mx-12" />
          }
        >
          {!isMobileView && !isDealsMainView && (
            <DealCategoryBreadcrumb className="px-6 md:px-12" />
          )}

          <div
            data-category={category}
            className="translate-y-5 animate-fade-in-up opacity-0"
          >
            {isDealsMainView ? (
              <DealMainSection />
            ) : (
              <DealsCategorySection className="px-6 md:px-12" />
            )}
          </div>
        </Suspense>
      </div>
      <Suspense>
        <SubscribeNewsletterPopup />
      </Suspense>
    </>
  );
};

export const DealsPage = () => {
  const isSearchActive = useUnit(mobileSearchModel.$isSearchActive);
  const isMobileView = useIsMobileView();
  const navigate = routeApi.useNavigate();

  const { title, category } = routeApi.useSearch();

  const isSearchQueryPresent = category || title;

  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const [searchQuery, setSearchQuery] = useState('');

  const debouncedOnDealSearch = useRef(
    debounce((title: string) => {
      navigate({
        replace: true,
        search: title
          ? {
              title: title,
            }
          : undefined,
      });
    }, 300),
  ).current;

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    debouncedOnDealSearch(value);
  };

  const onClear = () => {
    handleSearchChange('');

    navigate({
      replace: true,
      search: undefined,
    });
  };

  const onCloseSearch = () => {
    if (isSearchQueryPresent) {
      setSearchQuery('');
      navigate({
        replace: true,
        search: undefined,
      });

      return;
    }

    mobileSearchModel.closeSearch();
  };

  return (
    <>
      {isSearchActive && isMobileView ? (
        <MobileSearch
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder={t(LOCIZE_DEALS_KEYS.dealsSearch)}
          onClear={onClear}
          onCloseSearch={onCloseSearch}
        >
          <DealsMobileSearchView />
        </MobileSearch>
      ) : (
        <Deals />
      )}
    </>
  );
};
