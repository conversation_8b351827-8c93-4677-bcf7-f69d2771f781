import { LOCIZE_NAMESPACES, LOCIZE_SIDEBAR_KEYS } from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { useAppConfig, useFeatureToggles } from '@hooks/system';
import PremiumIcon from '@icons/crown.svg?react';
import DealsIcon from '@icons/deals.svg?react';
import AgreementsIcon from '@icons/file-check.svg?react';
import DashboardIcon from '@icons/home.svg?react';
import SupportIcon from '@icons/message-circle-question.svg?react';
import CreditLineIcon from '@icons/products/credit-line.svg?react';
import ProfileIcon from '@icons/profile.svg?react';
import InvoicesIcon from '@icons/scroll-text.svg?react';
import InsuranceIcon from '@icons/shield.svg?react';
import { useTranslation } from 'react-i18next';

export const useMainLayout = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.sidebar);
  const { supportUrl } = useAppConfig();
  const featureToggles = useFeatureToggles();

  const sidebarNavLinks = [
    {
      icon: DashboardIcon,
      title: t(LOCIZE_SIDEBAR_KEYS.dashboardNavItem),
      href: ROUTE_NAMES.dashboard,
    },
    {
      href: ROUTE_NAMES.creditLine,
      title: t(LOCIZE_SIDEBAR_KEYS.creditLineNavItem),
      icon: CreditLineIcon,
    },
    featureToggles.invoicesFeature
      ? {
          href: ROUTE_NAMES.invoices,
          title: t(LOCIZE_SIDEBAR_KEYS.invoicesNavItem),
          icon: InvoicesIcon,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.invoices,
          title: t(LOCIZE_SIDEBAR_KEYS.invoicesNavItem),
          icon: InvoicesIcon,
        },
    featureToggles.agreementsFeature
      ? {
          href: ROUTE_NAMES.agreements,
          title: t(LOCIZE_SIDEBAR_KEYS.agreementsNavItem),
          icon: AgreementsIcon,
          withSeparator: true,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.contracts,
          title: t(LOCIZE_SIDEBAR_KEYS.agreementsNavItem),
          icon: AgreementsIcon,
          withSeparator: true,
        },

    ...(featureToggles.dealsFeature
      ? [
          {
            href: ROUTE_NAMES.deals,
            title: t(LOCIZE_SIDEBAR_KEYS.dealsNavItem),
            icon: DealsIcon,
            isNew: true,
          },
        ]
      : []),
    ...(featureToggles.insuranceFeature
      ? [
          {
            href: ROUTE_NAMES.insurance,
            title: t(LOCIZE_SIDEBAR_KEYS.insuranceNavItem),
            icon: InsuranceIcon,
            isNew: true,
          },
        ]
      : []),

    {
      href: ROUTE_NAMES.premium,
      title: t(LOCIZE_SIDEBAR_KEYS.premiumNavItem),
      icon: PremiumIcon,
      withSeparator: true,
    },

    {
      isExternal: true,
      isOpenInNewTab: true,
      href: supportUrl,
      title: t(LOCIZE_SIDEBAR_KEYS.supportNavItem),
      icon: SupportIcon,
    },
    featureToggles.profileFeature
      ? {
          href: ROUTE_NAMES.profile,
          title: t(LOCIZE_SIDEBAR_KEYS.profileNavItem),
          icon: ProfileIcon,
        }
      : {
          isExternal: true,
          href: OLD_APP_ROUTE_NAME.profile,
          title: t(LOCIZE_SIDEBAR_KEYS.profileNavItem),
          icon: ProfileIcon,
        },
  ];

  return {
    sidebarNavLinks,
  };
};
