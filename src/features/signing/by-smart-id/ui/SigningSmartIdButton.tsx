import { Typography } from '@components/typography';
import { Button, type ButtonProps } from '@components/ui/button';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_SIGNING_KEYS } from '@config/locize/signing';
import { smartIdSigningApi } from '@features/signing/by-smart-id/api';
import { useQueryClient } from '@tanstack/react-query';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import type {
  Application,
  ContractType,
  CreditAccount,
  User,
} from '@/shared/types';

import { SigningSmartIdButtonPollingView } from './SigningSmartIdButtonPollingView';

enum SigningSmartIdView {
  BUTTON = 0,
  POLLING = 1,
}

type SigningSmartIdButtonProps = {
  creditAccountId?: CreditAccount['id'];
  applicationId?: Application['id'];
  userId?: User['id'];
  contractType: ContractType;
  buttonComponent?: FC<ButtonProps>;
  disabled?: boolean;
  onSuccess: () => void;
  onError: (error: unknown) => void;
  onReject: () => void;
  className?: string;
  label?: string;
};

export const SigningSmartIdButton: FC<SigningSmartIdButtonProps> = ({
  userId,
  creditAccountId,
  applicationId,
  contractType,
  disabled,
  onSuccess,
  onError,
  onReject,
  buttonComponent: ButtonComponent = Button,
  className,
  label,
}) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);
  const { t: tSigning } = useTranslation(LOCIZE_NAMESPACES.signing);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [view, setView] = useState<SigningSmartIdView>(
    SigningSmartIdView.BUTTON,
  );

  const smartIdSigningMutation =
    smartIdSigningApi.useSmartIdSignContractMutation({
      onSuccess: async (data) => {
        if (!data?.success) {
          onReject();

          return;
        }

        queryClient.setQueryData(['PrepareSmartIdSignature'], null);

        onSuccess();
      },
      onError,
    });

  const prepareSmartIdSignatureMutation =
    smartIdSigningApi.usePrepareSmartIdSignatureMutation({
      onSuccess: async (data) => {
        if (!data?.challenge?.challenge_id) {
          queryClient.setQueryData(['PrepareSmartIdSignature'], null);
          onReject();

          return;
        }

        queryClient.setQueryData(['PrepareSmartIdSignature'], data);

        setView(SigningSmartIdView.POLLING);

        const id = setTimeout(() => {
          smartIdSigningMutation.mutate({});
        }, 3000);

        setTimeoutId(id);
      },
      onError,
    });

  const handleCancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }

    queryClient.setQueryData(['PrepareSmartIdSignature'], null);

    setView(SigningSmartIdView.BUTTON);
  };

  const handleButtonClick = () => {
    prepareSmartIdSignatureMutation.mutate({
      user_id: userId,
      contract_type: contractType,
      credit_account_id: creditAccountId,
      application_id: applicationId,
    });
  };

  if (prepareSmartIdSignatureMutation.isPending) {
    return (
      <div className="flex flex-col items-center gap-4 p-4">
        <LoadingSpinner />
        <div className="flex flex-col gap-2 text-center">
          <Typography variant="s" className="font-semibold">
            {tSigning(LOCIZE_SIGNING_KEYS.preparingSigningHeading)}
          </Typography>
          <Typography variant="text-m" className="text-gray-600">
            {tSigning(LOCIZE_SIGNING_KEYS.openSmartIdOnYourDevice)}
          </Typography>
        </div>
      </div>
    );
  }

  if (smartIdSigningMutation.isPending) {
    return (
      <div className="flex flex-col items-center gap-4 p-4">
        <LoadingSpinner />
        <div className="flex flex-col gap-2 text-center">
          <Typography variant="s" className="font-semibold">
            {tSigning(LOCIZE_SIGNING_KEYS.signingContractHeading)}
          </Typography>
          <Typography variant="text-m" className="text-gray-600">
            {tSigning(LOCIZE_SIGNING_KEYS.keepPageOpenDisclaimer)}
          </Typography>
        </div>
      </div>
    );
  }

  if (view === SigningSmartIdView.POLLING) {
    return (
      <SigningSmartIdButtonPollingView
        handleCancel={handleCancel}
        smartIdSigningMutation={smartIdSigningMutation}
        challengeId={
          prepareSmartIdSignatureMutation.data?.challenge?.challenge_id || ''
        }
      />
    );
  }

  return (
    <ButtonComponent
      onClick={handleButtonClick}
      loading={
        prepareSmartIdSignatureMutation.isPending ||
        smartIdSigningMutation.isPending
      }
      disabled={disabled}
      className={className}
    >
      {label || t(LOCIZE_COMMON_KEYS.applyButton)}
    </ButtonComponent>
  );
};
