import { Typography } from '@components/typography';
import { Button, type ButtonProps } from '@components/ui/button';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_SIGNING_KEYS } from '@config/locize/signing';
import { queryClient } from '@lib/queryClient';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { LoadingSpinner } from '@/shared/components/LoadingSpinner';
import type {
  Application,
  ContractType,
  CreditAccount,
  User,
} from '@/shared/types';

import { mobileIdSigningApi } from '../api';
import { SigningMobileIdButtonPollingView } from './SigningMobileIdButtonPollingView';

enum SigningMobileIdView {
  BUTTON = 0,
  POLLING = 1,
}

type SigningMobileIdButtonProps = {
  userId?: User['id'];
  creditAccountId?: CreditAccount['id'];
  applicationId?: Application['id'];
  contractType: ContractType;
  buttonComponent?: FC<ButtonProps>;
  disabled?: boolean;
  onSuccess: () => void;
  onError: (error: unknown) => void;
  onReject: () => void;
  className?: string;
  label?: string;
};

export const SigningMobileIdButton: FC<SigningMobileIdButtonProps> = ({
  userId,
  creditAccountId,
  applicationId,
  contractType,
  disabled,
  onSuccess,
  onError,
  onReject,
  buttonComponent: ButtonComponent = Button,
  className,
  label,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);
  const { t: tSigning } = useTranslation(LOCIZE_NAMESPACES.signing);

  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [view, setView] = useState<SigningMobileIdView>(
    SigningMobileIdView.BUTTON,
  );

  const mobileIdSigningMutation =
    mobileIdSigningApi.useMobileIdSignContractMutation({
      onSuccess: async (data) => {
        if (!data?.success) {
          onReject();
          return;
        }

        queryClient.setQueryData(['PrepareMobileIdSignature'], null);

        onSuccess();
      },
      onError,
    });

  const prepareMobileIdSignatureMutation =
    mobileIdSigningApi.usePrepareMobileIdSignatureMutation({
      onSuccess: async (data) => {
        if (!data?.challenge?.challenge_id) {
          queryClient.setQueryData(['PrepareMobileIdSignature'], null);
          onReject();
          return;
        }

        queryClient.setQueryData(['PrepareMobileIdSignature'], data);

        setView(SigningMobileIdView.POLLING);

        const id = setTimeout(() => {
          mobileIdSigningMutation.mutate({});
        }, 3000);

        setTimeoutId(id);
      },
      onError,
    });

  const handleCancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }

    queryClient.setQueryData(['PrepareMobileIdSignature'], null);

    setView(SigningMobileIdView.BUTTON);
  };

  const handleButtonClick = () => {
    prepareMobileIdSignatureMutation.mutate({
      user_id: userId,
      contract_type: contractType,
      credit_account_id: creditAccountId,
      application_id: applicationId,
    });
  };

  if (prepareMobileIdSignatureMutation.isPending) {
    return (
      <div className="flex flex-col items-center gap-4 p-4">
        <LoadingSpinner />
        <div className="flex flex-col gap-2 text-center">
          <Typography variant="s" className="font-semibold">
            {tSigning(LOCIZE_SIGNING_KEYS.preparingSigningHeading)}
          </Typography>
          <Typography variant="text-m" className="text-gray-600">
            {tSigning(LOCIZE_SIGNING_KEYS.openMobileIdOnYourDevice)}
          </Typography>
        </div>
      </div>
    );
  }

  if (mobileIdSigningMutation.isPending) {
    return (
      <div className="flex flex-col items-center gap-4 p-4">
        <LoadingSpinner />
        <div className="flex flex-col gap-2 text-center">
          <Typography variant="s" className="font-semibold">
            {tSigning(LOCIZE_SIGNING_KEYS.signingContractHeading)}
          </Typography>
          <Typography variant="text-m" className="text-gray-600">
            {tSigning(LOCIZE_SIGNING_KEYS.keepPageOpenDisclaimer)}
          </Typography>
        </div>
      </div>
    );
  }

  if (view === SigningMobileIdView.POLLING) {
    return (
      <SigningMobileIdButtonPollingView
        handleCancel={handleCancel}
        mobileIdSigningMutation={mobileIdSigningMutation}
        challengeId={
          prepareMobileIdSignatureMutation.data?.challenge?.challenge_id || ''
        }
      />
    );
  }

  return (
    <ButtonComponent
      onClick={handleButtonClick}
      disabled={disabled}
      className={className}
    >
      {label || t(LOCIZE_COMMON_KEYS.applyButton)}
    </ButtonComponent>
  );
};
